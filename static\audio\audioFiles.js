// 2048游戏音效文件配置
// 包含五个音效：移动、新游戏、胜利、游戏结束、点击

const audioConfig = {
    // 音效文件路径配置
    paths: {
        move: '/static/audio/move.mp3',
        newGame: '/static/audio/newgame.mp3',
        win: '/static/audio/win.mp3',
        gameOver: '/static/audio/gameover.mp3',
        click: '/static/audio/click.mp3'
    },

    // 音效参数配置
    settings: {
        volume: 0.6,
        fadeIn: false,
        fadeOut: false,
        clickThrottle: 100 // 点击音效限制间隔（毫秒）
    },

    // 音效说明
    descriptions: {
        move: '移动音效 - 方块移动时播放',
        newGame: '新游戏音效 - 开始新游戏时播放',
        win: '胜利音效 - 达成2048时播放',
        gameOver: '游戏结束音效 - 游戏失败时播放',
        click: '点击音效 - 用户点击任意位置时播放'
    }
};

module.exports = audioConfig;