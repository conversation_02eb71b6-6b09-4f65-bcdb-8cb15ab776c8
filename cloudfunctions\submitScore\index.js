// 云函数：提交2048游戏分数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { nickname, score, moves, gameTime, avgMoveTime, timestamp, deviceInfo } = event

  try {
    // 数据验证
    if (!nickname || !score || score < 0) {
      return {
        success: false,
        message: '数据格式错误'
      }
    }

    // 昵称长度限制
    if (nickname.length > 10) {
      return {
        success: false,
        message: '昵称不能超过10个字符'
      }
    }

    // 分数合理性检查（防止作弊）
    if (score > 1000000) {
      return {
        success: false,
        message: '分数异常，请联系小程序客服'
      }
    }

    // 防作弊检查
    if (gameTime && gameTime < 3000) {
      return {
        success: false,
        message: '游戏时间过短，请正常游戏'
      }
    }

    if (avgMoveTime && avgMoveTime < 200) {
      return {
        success: false,
        message: '操作过快，请正常游戏'
      }
    }

    // 获取用户openid
    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    // 检查是否重复提交（同一用户1分钟内不能提交相同分数）
    const recentSubmissions = await db.collection('game2048_scores')
      .where({
        _openid: openid,
        score: score,
        createdAt: db.command.gte(new Date(Date.now() - 60000)) // 1分钟内
      })
      .get()

    if (recentSubmissions.data.length > 0) {
      return {
        success: false,
        message: '请勿重复提交相同分数'
      }
    }

    // 插入分数记录
    const result = await db.collection('game2048_scores').add({
      data: {
        nickname: nickname,
        score: score,
        moves: moves,
        gameTime: gameTime,
        avgMoveTime: avgMoveTime,
        timestamp: timestamp || new Date().toISOString(),
        deviceInfo: deviceInfo || {},
        _openid: openid,
        createdAt: db.serverDate()
      }
    })

    // 获取用户当前排名（基于去重后的排行榜）
    const allScores = await db.collection('game2048_scores')
      .orderBy('score', 'desc')
      .orderBy('moves', 'asc')
      .orderBy('createdAt', 'asc')
      .get()

    // 去重处理（每个用户只保留最高分）
    const userBestScores = new Map()
    allScores.data.forEach(record => {
      const key = record._openid
      if (!userBestScores.has(key) || userBestScores.get(key).score < record.score) {
        userBestScores.set(key, record)
      }
    })

    // 计算排名
    const sortedScores = Array.from(userBestScores.values())
      .sort((a, b) => {
        if (b.score !== a.score) return b.score - a.score
        if (a.moves !== b.moves) return a.moves - b.moves
        return new Date(a.createdAt) - new Date(b.createdAt)
      })

    const rank = sortedScores.findIndex(record => record._openid === openid) + 1

    return {
      success: true,
      message: '分数提交成功',
      data: {
        id: result._id,
        rank: rank || sortedScores.length
      }
    }

  } catch (error) {
    console.error('提交分数失败:', error)
    return {
      success: false,
      message: '服务器错误，请重试'
    }
  }
}
