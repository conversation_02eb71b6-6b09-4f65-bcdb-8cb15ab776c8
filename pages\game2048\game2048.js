// pages/game2048/game2048.js
const audioManager = require('../../utils/audioManager');
const serverConfig = require('../../config/server-config');

Page({
    data: {
        // 游戏状态
        board: [],
        score: 0,
        bestScore: 0,
        moves: 0,
        gameOver: false,
        isWin: false,
        isNewRecord: false,
        
        // 触摸相关
        touchStartX: 0,
        touchStartY: 0,
        
        // 排行榜相关
        showRankingModal: false,
        loadingRanking: false,
        rankingList: [],
        
        // 提交分数相关
        nickname: '',
        submitting: false,

        // 防作弊相关
        gameStartTime: 0,
        totalMoveTime: 0,
        lastMoveTime: 0,

        // 音效相关
        soundEnabled: audioManager.soundEnabled,

        // 排名相关
        currentRank: 0,
        loadingRank: false,

        // 点击音效控制
        lastClickTime: 0,
        
        // 使用微信云开发，无需配置API地址
    },

    onLoad() {
        // 初始化游戏
        this.initGame();
        // 加载最高分
        this.loadBestScore();
        // 加载用户昵称
        this.loadNickname();
        // 禁用页面滚动
        this.disablePageScroll();
        // 同步音效设置
        this.setData({
            soundEnabled: audioManager.soundEnabled
        });
    },

    onUnload() {
        // 恢复页面滚动
        this.enablePageScroll();
        // 清理音频资源
        try {
            audioManager.destroy();
        } catch (e) {
            console.log('清理音频资源失败:', e);
        }
    },

    onShow() {
        // 页面显示时重新初始化音频管理器
        try {
            audioManager.reinitialize();
            // 同步音效设置
            this.setData({
                soundEnabled: audioManager.soundEnabled
            });
        } catch (e) {
            console.log('页面显示时初始化音频管理器失败:', e);
        }
    },

    onHide() {
        // 页面隐藏时不销毁音频管理器，只是暂停播放
        console.log('页面隐藏，音频管理器保持活跃状态');
    },

    // 禁用页面滚动
    disablePageScroll() {
        wx.pageScrollTo({
            scrollTop: 0,
            duration: 0
        });
    },

    // 恢复页面滚动
    enablePageScroll() {
        // 页面卸载时恢复滚动（如果需要）
    },

    // 播放音效
    playSound(type) {
        try {
            // 确保音频管理器可用
            if (audioManager.isDestroyed) {
                console.log('音频管理器已销毁，重新初始化');
                audioManager.reinitialize();
            }

            switch(type) {
                case 'move':
                    audioManager.playMoveSound();
                    break;
                case 'newGame':
                    audioManager.playNewGameSound();
                    break;
                case 'win':
                    audioManager.playWinSound();
                    break;
                case 'gameOver':
                    audioManager.playGameOverSound();
                    break;
                case 'click':
                    audioManager.playClickSound();
                    break;
                case 'ranking':
                    audioManager.playRankingSound();
                    break;
                default:
                    console.log('未知音效类型:', type);
            }
        } catch (e) {
            console.log('播放音效失败:', type, e);
        }
    },

    // 切换音效
    toggleSound() {
        const newSoundEnabled = audioManager.toggleSound();
        this.setData({
            soundEnabled: newSoundEnabled
        });

        // 播放测试音效
        if (newSoundEnabled) {
            this.playSound('move');
        }

        wx.showToast({
            title: newSoundEnabled ? '🔊 ikun音效已开启（请在页面找出一些ikun声音）' : '🔇 ikun音效已关闭',
            icon: 'none',
            duration: 2000
        });
    },

    // 测试点击音效（调试用）
    testClickSound() {
        console.log('=== 手动测试点击音效 ===');
        audioManager.testClickSound();
    },

    // 初始化游戏
    initGame() {
        const board = this.createEmptyBoard();
        this.addRandomTile(board);
        this.addRandomTile(board);
        
        this.setData({
            board: board,
            score: 0,
            moves: 0,
            gameOver: false,
            isWin: false,
            isNewRecord: false,
            gameStartTime: Date.now(),
            totalMoveTime: 0,
            lastMoveTime: 0
        });
    },

    // 创建空棋盘
    createEmptyBoard() {
        const board = [];
        for (let i = 0; i < 4; i++) {
            board[i] = [];
            for (let j = 0; j < 4; j++) {
                board[i][j] = 0;
            }
        }
        return board;
    },

    // 添加随机数字块
    addRandomTile(board) {
        const emptyCells = [];
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (board[i][j] === 0) {
                    emptyCells.push({row: i, col: j});
                }
            }
        }
        
        if (emptyCells.length > 0) {
            const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
            board[randomCell.row][randomCell.col] = Math.random() < 0.9 ? 2 : 4;
        }
    },

    // 触摸开始
    onTouchStart(e) {
        // 阻止默认滑动行为
        e.preventDefault && e.preventDefault();

        this.setData({
            touchStartX: e.touches[0].clientX,
            touchStartY: e.touches[0].clientY
        });
    },

    // 触摸结束
    onTouchEnd(e) {
        // 阻止默认滑动行为
        e.preventDefault && e.preventDefault();

        if (this.data.gameOver) return;

        const deltaX = e.changedTouches[0].clientX - this.data.touchStartX;
        const deltaY = e.changedTouches[0].clientY - this.data.touchStartY;
        const minDistance = 50; // 最小滑动距离

        if (Math.abs(deltaX) < minDistance && Math.abs(deltaY) < minDistance) {
            return; // 滑动距离太小，忽略
        }

        let direction = '';
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            // 水平滑动
            direction = deltaX > 0 ? 'right' : 'left';
        } else {
            // 垂直滑动
            direction = deltaY > 0 ? 'down' : 'up';
        }

        this.move(direction);
    },

    // 触摸移动（阻止页面滚动）
    onTouchMove(e) {
        // 阻止页面滚动
        e.preventDefault && e.preventDefault();
        return false;
    },

    // 全局点击事件
    onGlobalClick() {
        try {
            // 限制点击音效频率，避免过于频繁
            const currentTime = Date.now();
            if (currentTime - this.data.lastClickTime > 100) { // 100ms内只播放一次
                this.setData({
                    lastClickTime: currentTime
                });
                console.log('触发点击音效');
                this.playSound('click');
            }
        } catch (e) {
            console.log('全局点击事件处理失败:', e);
        }
    },



    // 移动逻辑
    move(direction) {
        const newBoard = JSON.parse(JSON.stringify(this.data.board));
        let moved = false;
        let scoreIncrease = 0;
        
        if (direction === 'left') {
            for (let i = 0; i < 4; i++) {
                const result = this.moveRowLeft(newBoard[i]);
                newBoard[i] = result.row;
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        } else if (direction === 'right') {
            for (let i = 0; i < 4; i++) {
                const result = this.moveRowRight(newBoard[i]);
                newBoard[i] = result.row;
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        } else if (direction === 'up') {
            for (let j = 0; j < 4; j++) {
                const column = [newBoard[0][j], newBoard[1][j], newBoard[2][j], newBoard[3][j]];
                const result = this.moveRowLeft(column);
                for (let i = 0; i < 4; i++) {
                    newBoard[i][j] = result.row[i];
                }
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        } else if (direction === 'down') {
            for (let j = 0; j < 4; j++) {
                const column = [newBoard[0][j], newBoard[1][j], newBoard[2][j], newBoard[3][j]];
                const result = this.moveRowRight(column);
                for (let i = 0; i < 4; i++) {
                    newBoard[i][j] = result.row[i];
                }
                moved = moved || result.moved;
                scoreIncrease += result.score;
            }
        }
        
        if (moved) {
            this.addRandomTile(newBoard);
            const newScore = this.data.score + scoreIncrease;
            const newMoves = this.data.moves + 1;
            const currentTime = Date.now();
            const moveTime = currentTime - (this.data.lastMoveTime || this.data.gameStartTime);

            // 播放移动音效
            this.playSound('move');

            this.setData({
                board: newBoard,
                score: newScore,
                moves: newMoves,
                totalMoveTime: this.data.totalMoveTime + moveTime,
                lastMoveTime: currentTime
            });

            // 检查游戏状态
            this.checkGameStatus(newBoard, newScore);
        }
    },

    // 向左移动一行
    moveRowLeft(row) {
        const newRow = row.filter(cell => cell !== 0);
        let moved = newRow.length !== row.length || !this.arraysEqual(newRow, row.filter(cell => cell !== 0));
        let score = 0;
        
        // 合并相同数字
        for (let i = 0; i < newRow.length - 1; i++) {
            if (newRow[i] === newRow[i + 1]) {
                newRow[i] *= 2;
                score += newRow[i];
                newRow.splice(i + 1, 1);
                moved = true;
            }
        }
        
        // 补齐到4位
        while (newRow.length < 4) {
            newRow.push(0);
        }
        
        return { row: newRow, moved, score };
    },

    // 向右移动一行
    moveRowRight(row) {
        const reversed = row.slice().reverse();
        const result = this.moveRowLeft(reversed);
        return {
            row: result.row.reverse(),
            moved: result.moved,
            score: result.score
        };
    },

    // 数组比较
    arraysEqual(a, b) {
        return a.length === b.length && a.every((val, i) => val === b[i]);
    },

    // 检查游戏状态
    checkGameStatus(board, score) {
        // 检查是否获胜（达到2048）
        let hasWon = false;
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (board[i][j] >= 2048) {
                    hasWon = true;
                    break;
                }
            }
        }
        
        // 检查是否还能移动
        const canMove = this.canMove(board);
        
        if (hasWon || !canMove) {
            const isNewRecord = score > this.data.bestScore;
            if (isNewRecord) {
                this.saveBestScore(score);
            }

            // 播放游戏结束音效
            if (hasWon) {
                this.playSound('win');
            } else {
                this.playSound('gameOver');
            }

            this.setData({
                gameOver: true,
                isWin: hasWon,
                isNewRecord: isNewRecord,
                bestScore: Math.max(score, this.data.bestScore),
                loadingRank: true,
                currentRank: 0
            });

            // 获取当前分数在排行榜中的位次
            this.getCurrentRank(score);

            // 显示游戏结束提示
            this.showGameOverToast(hasWon, score, isNewRecord);
        }
    },

    // 获取当前分数的排名
    getCurrentRank(score) {
        // 模拟获取排名（实际应该调用API）
        // 这里先用本地计算一个大概的排名
        const estimatedRank = this.estimateRank(score);

        this.setData({
            currentRank: estimatedRank,
            loadingRank: false
        });

        // 如果有API，可以调用真实的排名接口
        /*
        wx.request({
            url: 'https://your-api-domain.com/api/game2048/get-rank',
            method: 'POST',
            data: { score: score },
            success: (res) => {
                if (res.data && res.data.success) {
                    this.setData({
                        currentRank: res.data.rank || estimatedRank,
                        loadingRank: false
                    });
                }
            },
            fail: () => {
                this.setData({
                    currentRank: estimatedRank,
                    loadingRank: false
                });
            }
        });
        */
    },

    // 估算排名（基于分数）
    estimateRank(score) {
        if (score >= 50000) return Math.floor(Math.random() * 5) + 1;       // 前5名（超级高手）
        if (score >= 30000) return Math.floor(Math.random() * 15) + 6;      // 6-20名（顶级高手）
        if (score >= 20000) return Math.floor(Math.random() * 30) + 21;     // 21-50名（高手）
        if (score >= 15000) return Math.floor(Math.random() * 50) + 51;     // 51-100名（熟练+）
        if (score >= 8000) return Math.floor(Math.random() * 100) + 101;    // 101-200名（熟练）
        if (score >= 5000) return Math.floor(Math.random() * 150) + 201;    // 201-350名（新手+）
        if (score >= 2048) return Math.floor(Math.random() * 200) + 351;    // 351-550名（新手）
        return Math.floor(Math.random() * 300) + 551;                       // 551名以后
    },

    // 显示游戏结束提示
    showGameOverToast(hasWon, score, isNewRecord) {
        setTimeout(() => {
            const rank = this.data.currentRank;
            let title = hasWon ? '🎉 恭喜获胜！' : '😢 游戏结束';
            let content = `本次分数：${score}分\n`;

            if (isNewRecord) {
                content += '🏆 新的个人最高分！\n';
            }

            if (rank > 0) {
                if (rank <= 10) {
                    content += `🥇 华航排行榜第${rank}名！太棒了！`;
                } else if (rank <= 50) {
                    content += `🏅 华航${rank}名！很不错！`;
                } else if (rank <= 100) {
                    content += `📈 华航排行榜第${rank}名！继续加油！`;
                } else {
                    content += `📊 华航排行榜第${rank}名`;
                }
            } else {
                content += '快去提交分数到华航排行榜吧！';
            }

            wx.showModal({
                title: title,
                content: content,
                showCancel: false,
                confirmText: '知道了',
                confirmColor: hasWon ? '#27ae60' : '#e74c3c'
            });
        }, 500); // 延迟500ms显示，让用户看到最终状态
    },

    // 检查是否还能移动
    canMove(board) {
        // 检查是否有空格
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                if (board[i][j] === 0) {
                    return true;
                }
            }
        }
        
        // 检查是否有相邻相同数字
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 4; j++) {
                const current = board[i][j];
                if ((i < 3 && board[i + 1][j] === current) ||
                    (j < 3 && board[i][j + 1] === current)) {
                    return true;
                }
            }
        }
        
        return false;
    },

    // 新游戏（添加确认提示）
    newGame() {
        // 如果当前有分数（无论游戏是否结束），都显示确认对话框
        if (this.data.score > 0) {
            const gameStatus = this.data.gameOver ?
                (this.data.isWin ? '（已胜利）' : '（已结束）') :
                '（进行中）';

            wx.showModal({
                title: '开始新游戏',
                content: `当前分数：${this.data.score}${gameStatus}\n确定要开始新游戏吗？当前进度将丢失。`,
                confirmText: '确定',
                cancelText: '取消',
                confirmColor: '#e17055',
                success: (res) => {
                    if (res.confirm) {
                        this.startNewGame();
                    }
                }
            });
        } else {
            this.startNewGame();
        }
    },

    // 开始新游戏
    startNewGame() {
        this.playSound('newGame');
        this.initGame();
        wx.showToast({
            title: '新游戏开始！',
            icon: 'none',
            duration: 1000
        });
    },

    // 加载最高分
    loadBestScore() {
        const bestScore = wx.getStorageSync('game2048_best_score') || 0;
        this.setData({ bestScore });
    },

    // 保存最高分
    saveBestScore(score) {
        wx.setStorageSync('game2048_best_score', score);
    },

    // 加载用户昵称
    loadNickname() {
        const nickname = wx.getStorageSync('game2048_nickname') || '';
        this.setData({ nickname });
    },

    // 昵称输入
    onNicknameInput(e) {
        this.setData({
            nickname: e.detail.value
        });
    },

    // 提交分数到真实排行榜
    submitScore() {
        const { nickname, score, moves } = this.data;

        if (!nickname.trim()) {
            wx.showToast({
                title: '请输入昵称',
                icon: 'none'
            });
            return;
        }

        if (nickname.trim().length > 10) {
            wx.showToast({
                title: '昵称不能超过10个字符',
                icon: 'none'
            });
            return;
        }

        this.setData({ submitting: true });

        // 保存昵称到本地
        wx.setStorageSync('game2048_nickname', nickname.trim());

        // 提交分数到服务器
        this.submitScoreToServer(nickname.trim(), score, moves);
    },

    // 提交分数到服务器
    submitScoreToServer(nickname, score, moves) {
        const gameTime = Date.now() - this.data.gameStartTime;
        const avgMoveTime = this.data.totalMoveTime / moves;

        // 基本的防作弊检查
        if (gameTime < 3000) { // 游戏时间少于30秒
            wx.showToast({
                title: '游戏时间过短，请正常游戏',
                icon: 'none'
            });
            this.setData({ submitting: false });
            return;
        }

        if (avgMoveTime < 200) { // 平均每步少于200毫秒
            wx.showToast({
                title: '操作过快，请正常游戏',
                icon: 'none'
            });
            this.setData({ submitting: false });
            return;
        }

        const scoreData = {
            nickname: nickname,
            score: score,
            moves: moves,
            gameTime: gameTime || null,
            avgMoveTime: avgMoveTime || null,
            timestamp: new Date().toISOString(),
            deviceInfo: this.getDeviceInfo(),
            openid: null // 暂时设为null，后续可以获取微信openid
        };

        // 使用微信云开发或第三方API
        this.submitToCloudFunction(scoreData);
    },

    // 获取设备信息（用于防刷分）
    getDeviceInfo() {
        try {
            const systemInfo = wx.getSystemInfoSync();
            return {
                platform: systemInfo.platform,
                version: systemInfo.version,
                model: systemInfo.model
            };
        } catch (e) {
            return {};
        }
    },

    // 提交到云函数（推荐方案）
    submitToCloudFunction(scoreData) {
        // 直接使用HTTP API，不使用微信云开发
        console.log('跳过云函数，直接使用HTTP API');
        this.submitToHttpAPI(scoreData);
    },

    // 提交到HTTP API（备用方案）
    submitToHttpAPI(scoreData) {
        wx.request({
            url: serverConfig.getApiUrl('submitScore'), // 使用配置文件中的地址
            method: 'POST',
            header: {
                'Content-Type': 'application/json'
            },
            data: scoreData,
            success: (res) => {
                this.setData({ submitting: false });
                if (res.statusCode === 200 && res.data.success) {
                    wx.showToast({
                        title: '分数提交成功！',
                        icon: 'success'
                    });
                    // 刷新排行榜
                    this.loadRankingData();
                } else {
                    wx.showToast({
                        title: res.data.message || '提交失败',
                        icon: 'none'
                    });
                }
            },
            fail: (err) => {
                console.error('HTTP提交失败:', err);
                // 网络失败时保存到本地缓存
                this.saveScoreToLocalCache(scoreData);
            }
        });
    },

    // 保存分数到本地缓存（网络失败时的备用方案）
    saveScoreToLocalCache(scoreData) {
        try {
            // 获取现有的本地分数记录
            const localScores = wx.getStorageSync('game2048_local_scores') || [];

            // 添加新分数记录
            const newScore = {
                ...scoreData,
                id: Date.now(),
                timestamp: new Date().toISOString(),
                synced: false // 标记为未同步到服务器
            };

            localScores.unshift(newScore);

            // 只保留最近的50条记录
            const limitedScores = localScores.slice(0, 50);

            // 保存到本地存储
            wx.setStorageSync('game2048_local_scores', limitedScores);

            // 更新本地排行榜缓存
            this.updateLocalRankingCache(limitedScores);

            this.setData({ submitting: false });

            wx.showToast({
                title: '网络异常，分数已保存到本地',
                icon: 'none',
                duration: 2500
            });

            console.log('分数已保存到本地缓存:', newScore);

        } catch (error) {
            console.error('保存分数到本地失败:', error);
            this.setData({ submitting: false });
            wx.showToast({
                title: '保存失败，请重试',
                icon: 'none'
            });
        }
    },

    // 更新本地排行榜缓存
    updateLocalRankingCache(localScores) {
        // 按分数排序，生成排行榜
        const sortedScores = localScores
            .sort((a, b) => {
                if (b.score !== a.score) {
                    return b.score - a.score; // 分数降序
                }
                return a.moves - b.moves; // 步数升序
            })
            .slice(0, 20) // 只取前20名
            .map((score, index) => ({
                id: score.id,
                nickname: score.nickname,
                score: score.score,
                moves: score.moves,
                rank: index + 1,
                timeAgo: this.formatTimeAgo(score.timestamp),
                isLocal: true // 标记为本地数据
            }));

        // 更新排行榜缓存
        wx.setStorageSync('game2048_ranking_cache', sortedScores);

        console.log('本地排行榜已更新，共', sortedScores.length, '条记录');
    },

    // 显示排行榜
    showRanking() {
        // 播放华航排行榜特殊音效
        this.playSound('ranking');

        this.setData({
            showRankingModal: true,
            loadingRanking: true,
            rankingList: []
        });

        // 测试优化后的SSL连接
        this.loadRankingData();
    },

    // 加载排行榜数据（仅从服务器）
    async loadRankingData() {
        try {
            console.log('开始从服务器加载排行榜数据...');

            // 直接从API加载数据
            const apiData = await loadRankingFromAPIWithRetry();
            console.log('从API加载排行榜数据成功，共', apiData.length, '条记录');

            // 处理数据并更新界面
            const rankingList = this.processRankingData(apiData);
            this.setData({
                loadingRanking: false,
                rankingList: rankingList
            });

            return apiData;

        } catch (error) {
            console.error('排行榜加载错误:', error.message);

            // 如果API失败，显示空列表
            this.setData({
                loadingRanking: false,
                rankingList: []
            });

            this.handleRankingError('网络连接失败，请检查网络后重试');
            return [];
        }
    },

    // 从云函数加载排行榜
    loadRankingFromCloud() {
        wx.cloud.callFunction({
            name: 'getRanking',
            data: {
                limit: 50 // 获取前50名
            },
            success: (res) => {
                if (res.result.success) {
                    const rankingList = this.processRankingData(res.result.data);
                    this.setData({
                        loadingRanking: false,
                        rankingList: rankingList
                    });
                } else {
                    this.handleRankingError('加载排行榜失败');
                }
            },
            fail: (err) => {
                console.error('云函数加载排行榜失败:', err);
                // 直接降级到本地缓存，不使用HTTP API
                this.loadRankingFromLocalCache();
            }
        });
    },

    // 从HTTP API加载排行榜
    loadRankingFromAPI() {
        return new Promise((resolve, reject) => {
            console.log('开始请求排行榜API...');
            
            wx.request({
                url: 'https://api.huahang.me/api/game2048/ranking?limit=50',
                method: 'GET',
                timeout: 30000, // 增加到30秒
                header: {
                    'content-type': 'application/json',
                    'User-Agent': 'WeChat-MiniProgram'
                },
                enableHttp2: false, // 禁用HTTP2，使用HTTP1.1
                enableQuic: false,  // 禁用QUIC
                success: function(res) {
                    console.log('API请求成功:', res);
                    if (res.statusCode === 200 && res.data) {
                        if (res.data.success) {
                            resolve(res.data.data);
                        } else {
                            reject(new Error('API返回失败状态'));
                        }
                    } else {
                        reject(new Error(`HTTP状态码: ${res.statusCode}`));
                    }
                },
                fail: function(err) {
                    console.error('API请求失败:', err);
                    reject(new Error(`请求失败: ${err.errMsg}`));
                }
            });
        });
    },



    // 处理排行榜数据
    processRankingData(rawData) {
        return rawData.map((item, index) => ({
            id: item._id || item.id,
            nickname: item.nickname,
            score: item.score,
            moves: item.moves,
            timeAgo: this.formatTimeAgo(item.timestamp || item.createdAt),
            rank: index + 1
        }));
    },

    // 格式化时间
    formatTimeAgo(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;

        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 30) return `${days}天前`;
        return time.toLocaleDateString();
    },

    // 处理排行榜加载错误
    handleRankingError(message) {
        console.log('排行榜加载错误:', message);

        // 显示错误提示
        wx.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000
        });
    },

    // 关闭排行榜
    closeRanking() {
        this.setData({
            showRankingModal: false
        });
    },

    // 分享功能
    onShareAppMessage() {
        return {
            title: `我在华航2048小游戏中得了${this.data.score}分！马上华航第一！`,
            path: '/pages/game2048/game2048',
            imageUrl: '/images/share-2048.png'
        };
    },

    // 分享到朋友圈
    onShareTimeline() {
        return {
            title: `华航2048小游戏 - 我得了${this.data.score}分！马上华航第一`,
            query: '',
            imageUrl: '/images/share-2048.png'
        };
    }
});

// 添加带重试的请求函数
async function loadRankingFromAPIWithRetry() {
    const maxRetries = 2; // 减少重试次数
    let lastError;
    
    for (let i = 0; i < maxRetries; i++) {
        try {
            console.log(`尝试请求排行榜 (${i + 1}/${maxRetries})`);
            
            const result = await new Promise((resolve, reject) => {
                wx.request({
                    url: 'https://api.huahang.me/api/game2048/ranking?limit=50',
                    method: 'GET',
                    timeout: 15000, // 15秒超时
                    dataType: 'json',
                    success: function(res) {
                        console.log('API响应:', res);
                        if (res.statusCode === 200) {
                            if (res.data && res.data.success) {
                                resolve(res.data.data);
                            } else if (res.data && Array.isArray(res.data)) {
                                // 如果直接返回数组
                                resolve(res.data);
                            } else {
                                reject(new Error(`API返回格式错误: ${JSON.stringify(res.data)}`));
                            }
                        } else {
                            reject(new Error(`HTTP状态码: ${res.statusCode}`));
                        }
                    },
                    fail: function(err) {
                        console.error('请求失败详情:', err);
                        console.error('错误类型:', typeof err.errMsg);
                        console.error('完整错误对象:', JSON.stringify(err));

                        let errorMsg = '网络连接失败';
                        if (err.errMsg) {
                            if (err.errMsg.includes('CONNECTION_CLOSED')) {
                                errorMsg = 'SSL证书验证失败，请联系管理员';
                            } else if (err.errMsg.includes('timeout')) {
                                errorMsg = '请求超时，请重试';
                            } else {
                                errorMsg = `网络错误: ${err.errMsg}`;
                            }
                        }

                        reject(new Error(errorMsg));
                    }
                });
            });
            
            console.log('排行榜请求成功');
            return result;
            
        } catch (error) {
            lastError = error;
            console.log(`第${i + 1}次请求失败:`, error.message);
            
            if (i < maxRetries - 1) {
                // 等待后重试，增加等待时间
                const waitTime = (i + 1) * 5000; // 5秒, 10秒
                console.log(`等待${waitTime}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
    }

    throw lastError;
}
