// 服务器配置 - 年度更换时只需修改这个文件

const SERVER_CONFIG = {
    // 当前年度服务器配置
    current: {
        year: 2024,
        ip: '*************',
        domain: 'api.huahang.me', // 你的阿里云域名
        port: 4000,
        expires: '2025-07-28' // 到期时间
    },
    
    // 历史服务器记录（备查）
    history: [
        {
            year: 2024,
            ip: '*************',
            domain: 'api.huahang.me',
            period: '2024-07-28 到 2025-07-28'
        }
        // 下一年添加新记录
    ],
    
    // 获取当前API基础地址
    getBaseUrl() {
        if (this.current.domain) {
            return `https://${this.current.domain}`; // 使用HTTPS
        } else {
            return `http://${this.current.ip}:${this.current.port}`;
        }
    },
    
    // 获取完整API地址
    getApiUrl(endpoint) {
        const baseUrl = this.getBaseUrl();
        const endpoints = {
            submitScore: '/api/game2048/submit-score',
            getRanking: '/api/game2048/ranking',
            healthCheck: '/'
        };
        return baseUrl + endpoints[endpoint];
    },
    
    // 检查是否即将到期（提前30天提醒）
    checkExpiry() {
        const expireDate = new Date(this.current.expires);
        const now = new Date();
        const daysLeft = Math.ceil((expireDate - now) / (1000 * 60 * 60 * 24));
        
        if (daysLeft <= 30) {
            console.warn(`⚠️ 服务器将在${daysLeft}天后到期，请准备迁移！`);
            return { needMigration: true, daysLeft };
        }
        
        return { needMigration: false, daysLeft };
    }
};

module.exports = SERVER_CONFIG;
