# 2048游戏音效文件

## 🔊 音效说明

当前系统使用**真实音频文件**，包含完整的五种音效。

### 音效类型

1. **move.mp3** - 移动音效 ✅
   - 文件路径：`/static/audio/move.mp3`
   - 触发：方块移动时
   - 特点：短促、清脆

2. **newgame.mp3** - 新游戏音效 ✅
   - 文件路径：`/static/audio/newgame.mp3`
   - 触发：开始新游戏时
   - 特点：愉快、激励

3. **win.mp3** - 胜利音效 ✅
   - 文件路径：`/static/audio/win.mp3`
   - 触发：达成2048时
   - 特点：庆祝、欢快

4. **gameover.mp3** - 游戏结束音效 ✅
   - 文件路径：`/static/audio/gameover.mp3`
   - 触发：游戏失败时
   - 特点：低沉、遗憾

5. **click.mp3** - 点击音效 ✅
   - 文件路径：`/static/audio/click.mp3`
   - 触发：用户点击任意位置时
   - 特点：轻快、反馈
   - 限制：100ms内只播放一次，避免过于频繁

### 音效优化

- ✅ **频率控制**：点击音效有100ms的限制间隔
- ✅ **全局覆盖**：点击音效覆盖整个游戏界面
- ✅ **智能触发**：根据游戏状态播放对应音效
- ✅ **错误处理**：音效播放失败时不影响游戏功能

## 🎵 如何添加真实音效

### 方法1：使用在线音效库

1. 从以下网站下载免费音效：
   - Freesound.org
   - Zapsplat.com
   - Adobe Audition内置音效

2. 将音效文件放在 `static/audio/` 目录下：
   ```
   static/audio/
   ├── move.mp3      (短促滴答声，0.1秒)
   ├── merge.mp3     (和谐音调，0.15秒)
   ├── newgame.mp3   (清脆开始音，0.2秒)
   ├── gameover.mp3  (低沉结束音，0.3秒)
   └── win.mp3       (胜利音乐，0.5秒)
   ```

3. 修改 `utils/audioManager.js` 中的音效播放方法

### 方法2：使用音效生成器

运行 `static/audio/generateAudio.js` 生成基础音效文件。

### 方法3：录制自定义音效

使用手机录音功能录制：
- 敲击声（移动音效）
- 铃声（合并音效）
- 口哨声（新游戏音效）
- 叹气声（游戏结束音效）
- 欢呼声（胜利音效）

## ⚙️ 配置说明

音效配置在 `static/audio/audioFiles.js` 中，可以调整：
- 音量大小
- 文件路径
- 备用方案

## 🔧 开发者注意

1. 小程序音频文件需要在 `app.json` 中配置域名白名单
2. 音效文件建议小于50KB
3. 支持格式：MP3, AAC, WAV
4. 建议使用CDN托管音频文件

## 🎮 当前状态

✅ 震动反馈已实现
⏳ 真实音效待添加音频文件
🔧 可通过音效开关控制
