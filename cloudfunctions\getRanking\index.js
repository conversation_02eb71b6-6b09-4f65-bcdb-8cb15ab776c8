// 云函数：获取2048游戏排行榜
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { limit = 50 } = event
  
  try {
    // 获取排行榜数据（按分数降序，相同分数按步数升序）
    const result = await db.collection('game2048_scores')
      .orderBy('score', 'desc')
      .orderBy('moves', 'asc')
      .orderBy('createdAt', 'asc')
      .limit(Math.min(limit, 100)) // 最多返回100条
      .get()
    
    // 处理数据，去重（同一用户只保留最高分）
    const userBestScores = new Map()
    
    result.data.forEach(record => {
      const key = record._openid
      if (!userBestScores.has(key) || userBestScores.get(key).score < record.score) {
        userBestScores.set(key, {
          _id: record._id,
          nickname: record.nickname,
          score: record.score,
          moves: record.moves,
          timestamp: record.timestamp,
          createdAt: record.createdAt
        })
      }
    })
    
    // 转换为数组并重新排序
    const rankingList = Array.from(userBestScores.values())
      .sort((a, b) => {
        if (b.score !== a.score) {
          return b.score - a.score // 分数降序
        }
        if (a.moves !== b.moves) {
          return a.moves - b.moves // 步数升序
        }
        return new Date(a.createdAt) - new Date(b.createdAt) // 时间升序
      })
      .slice(0, limit)
    
    return {
      success: true,
      data: rankingList,
      total: rankingList.length
    }
    
  } catch (error) {
    console.error('获取排行榜失败:', error)
    return {
      success: false,
      message: '获取排行榜失败',
      data: []
    }
  }
}
