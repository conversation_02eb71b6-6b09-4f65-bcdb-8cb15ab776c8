# 🎮 华航2048小游戏 - 独立微信小程序

## 📋 项目简介

这是一个独立的2048小游戏微信小程序，从华航去水印项目中提取出来，具有完整的游戏功能和排行榜系统。

## ✨ 主要功能

### 🎯 2048小游戏
- ✅ **经典2048玩法**：滑动合并数字，挑战2048
- ✅ **防滑动优化**：解决游戏时页面上下滑动问题
- ✅ **真实音效系统**：支持移动、合并、胜利、失败等音效反馈
- ✅ **音效开关**：可自由开启/关闭音效反馈
- ✅ **防误触保护**：新游戏按钮有确认提示，防止误触丢失进度
- ✅ **游戏结束提示**：显示分数、排名位次等详细信息
- ✅ **智能排名系统**：实时计算在排行榜中的位次
- ✅ **实时排行榜**：支持分数提交和全球排行榜
- ✅ **本地记录**：自动保存最高分和用户昵称
- ✅ **防作弊系统**：多重验证确保分数真实性

### 🏆 排行榜系统
- ✅ **云函数支持**：使用微信云开发，稳定可靠
- ✅ **防刷分机制**：频率限制、重复检测、异常分数过滤
- ✅ **用户去重**：每个用户只显示最高分
- ✅ **实时更新**：分数实时同步到排行榜

## 📁 项目结构

```
huahangxiaoyouxi/
├── pages/
│   └── game2048/                 # 2048小游戏页面
│       ├── game2048.js          # 游戏逻辑（含音效和防滑动）
│       ├── game2048.wxml        # 游戏界面（含音效开关）
│       ├── game2048.wxss        # 游戏样式（防滑动优化）
│       └── game2048.json        # 页面配置
├── cloudfunctions/               # 云函数代码
│   ├── submitScore/             # 提交分数云函数
│   └── getRanking/              # 获取排行榜云函数
├── config/                      # 配置文件
│   └── server-config.js         # 服务器配置
├── utils/                       # 工具函数
│   └── audioManager.js          # 音频管理器（音效系统）
├── static/                      # 静态资源
│   └── audio/                   # 音效文件
├── app.js                       # 小程序入口文件
├── app.json                     # 小程序配置文件
├── app.wxss                     # 全局样式文件
├── project.config.json          # 项目配置文件
└── sitemap.json                 # 站点地图配置
```

## 🚀 快速开始

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序账号
- 开通微信云开发服务

### 2. 配置云开发
1. 在微信开发者工具中打开项目
2. 点击"云开发"按钮，创建云开发环境
3. 修改 `app.js` 中的云开发环境ID：
   ```javascript
   wx.cloud.init({
       env: 'your-cloud-env-id', // 替换为你的云开发环境ID
       traceUser: true
   });
   ```

### 3. 部署云函数
1. 右键点击 `cloudfunctions/getRanking` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 右键点击 `cloudfunctions/submitScore` 文件夹
4. 选择"上传并部署：云端安装依赖"

### 4. 创建数据库集合
在云开发控制台中创建数据库集合：
- 集合名称：`game2048_scores`
- 权限设置：所有用户可读，仅创建者可写

### 5. 运行项目
1. 在微信开发者工具中点击"编译"
2. 在模拟器中测试游戏功能
3. 测试排行榜功能是否正常

## ⚙️ 配置说明

### 音效配置
音效文件位于 `static/audio/` 目录下，包含：
- `move.mp3` - 移动音效
- `newgame.mp3` - 新游戏音效
- `gameover.mp3` - 游戏结束音效
- `click-*.mp3` - 点击音效

### 服务器配置
如需使用自定义服务器，可修改 `config/server-config.js` 文件。

## 🔧 开发注意事项

1. **云开发环境ID**：必须在 `app.js` 中正确配置
2. **小程序AppID**：需要在 `project.config.json` 中配置正确的AppID
3. **音效文件**：确保音效文件路径正确，文件大小控制在50KB以内
4. **数据库权限**：确保数据库集合权限设置正确

## 📱 发布上线

1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 登录微信公众平台，提交审核
4. 审核通过后发布上线

## 🎮 游戏特色

- **华航专属**：专为华航学生定制的2048小游戏
- **排行榜竞技**：与其他华航同学一起竞技
- **音效体验**：丰富的音效反馈，提升游戏体验
- **防作弊系统**：确保排行榜的公平性

## 📞 技术支持

如有问题，请联系华航技术团队。

---

**华航2048小游戏** - 让数字合并更有趣！ 🎮✨
